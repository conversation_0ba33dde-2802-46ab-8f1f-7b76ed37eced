"use client";

import React, { useState, useEffect } from "react";
import { Sidebar } from "@/components/Dashboard/DashboardV2/components/Sidebar";
import supabase from "@/lib/supabaseClient";

/**
 * Common Dashboard layout component with fixed sidebar
 *
 * This component provides a consistent layout for all dashboard pages
 * with a fixed sidebar that doesn't scroll with the content.
 */
export default function DashboardLayout({ children, currentTab = "files" }) {
  const [isAnonymous, setIsAnonymous] = useState(true);

  useEffect(() => {
    const checkSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setIsAnonymous(session?.user?.is_anonymous);
    };

    checkSession();
  }, []);


  return (
    <div className="flex h-screen overflow-hidden bg-white">
      {/* Fixed sidebar */}
      <Sidebar
        className="hidden md:flex h-screen flex-shrink-0 sticky top-0"
        isAnonymous={isAnonymous}
      />

      {/* Scrollable main content */}
      <div className="flex-1 overflow-y-auto p-4 md:p-8">
        {children}
      </div>
    </div>
  );
}
