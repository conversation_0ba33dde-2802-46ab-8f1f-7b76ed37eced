"use client";

import { FileText, Plus, User } from "lucide-react";
import { Link } from "@/components/Common/Link";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

export function MobileNavBar({ currentTab = "files", onTabChange }) {
  const t = useTranslations("dashboard.mobileDashboard");

  const tabs = [
    {
      id: "files",
      icon: FileText,
      label: t("files"),
      onClick: () => onTabChange("files")
    },
    {
      id: "new",
      icon: Plus,
      label: t("new"),
      onClick: () => onTabChange("new")
    },
    {
      id: "account",
      icon: User,
      label: t("account"),
      onClick: () => onTabChange("account")
    }
  ];

  return (
    <div className="h-14 bg-white border-t border-gray-200 flex items-center justify-around fixed bottom-0 left-0 right-0">
      {tabs.map((tab) => {
        const Icon = tab.icon;
        const isActive = currentTab === tab.id;

        const TabComponent = tab.href ? Link : "button";
        const tabProps = tab.href 
          ? { href: tab.href } 
          : { onClick: tab.onClick };

        return (
          <TabComponent
            key={tab.id}
            className={cn(
              "flex flex-col items-center flex-1 h-full pt-2",
              isActive && "text-custom-bg"
            )}
            {...tabProps}
          >
            <Icon className={cn(
              "w-6 h-6",
              isActive && "text-custom-bg"
            )} />
            <span className="text-xs mt-1">{tab.label}</span>
          </TabComponent>
        );
      })}
    </div>
  );
} 