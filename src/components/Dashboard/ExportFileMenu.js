"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Download, Info, Gem } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";

import { TASK_STATUS } from "@/constants/task";
import UpgradeDialog from "@/components/Dashboard/UpgradeDialog";
import { Button } from "@/components/ui/button";
import { transcriptionService } from "@/services/api/transcriptionService";
import { shareService } from "@/services/api/shareService";
import { trackEvent } from "@/lib/analytics";
import { handleFileDownload } from "@/lib/downloadHelper";
import { useAuthStore } from "@/stores/useAuthStore";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";
import { trackAnonymousEvent } from "@/lib/analytics";

export default function ExportFileMenu({
  fileId,
  planConfig,
  duration,
  isSharedPage = false,
  transcriptionType = null,
  isAnonymous = false,
  insufficientMinutes = 0,
  taskStatuses,
}) {
  const t = useTranslations("dashboard.export");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);

  // 新增状态管理
  const [transcriptEnabled, setTranscriptEnabled] = useState(true);
  const [audioEnabled, setAudioEnabled] = useState(false);
  const [transcriptFormat, setTranscriptFormat] = useState("txt");
  const [showSpeaker, setShowSpeaker] = useState(true);
  const [showTimestamp, setShowTimestamp] = useState(false);

  const { user } = useAuthStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const router = useRouter();

  const hasIncompleteContent = insufficientMinutes > 0;

  // 使用 transcription.taskStatuses?.transcription === "completed" 来判断转录任务是否已完成
  const isTranscriptionComplete = taskStatuses?.transcription === TASK_STATUS.COMPLETED;

  // 转录格式定义
  const transcriptFormats = [
    { value: "txt", label: t("txt"), premium: false },
    { value: "docx", label: t("docx"), premium: true },
    { value: "pdf", label: t("pdf"), premium: true },
    { value: "csv", label: t("csv"), premium: true },
    {
      value: "srt",
      label: t("srt"),
      premium: false,
      needsWarning: transcriptionType === "transcript",
    },
    {
      value: "vtt",
      label: t("vtt"),
      premium: false,
      needsWarning: transcriptionType === "transcript",
    },
  ];

  // 音频格式定义（暂时只支持 MP3）
  const audioFormats = [
    { value: "mp3", label: "MP3 (.mp3)" },
  ];

  const checkExportAvailability = (format) => {
    return planConfig?.exportFormats?.includes(format);
  };

  // 获取当前选择的转录格式信息
  const selectedTranscriptFormat = transcriptFormats.find((f) => f.value === transcriptFormat);
  const isPremiumFormat = selectedTranscriptFormat?.premium;

  const handleExport = async () => {
    if (!transcriptEnabled && !audioEnabled) return;

    try {
      // 导出转录文件
      if (transcriptEnabled) {
        const isAvailable = checkExportAvailability(transcriptFormat);

        trackEvent("export_click", {
          format: transcriptFormat,
          action: isAvailable ? "export" : "upgrade_prompt",
          showSpeaker,
          showTimestamp,
        });

        if (isAvailable) {
          await exportTranscriptFile(fileId, transcriptFormat, {
            showSpeaker,
            showTimestamp,
          });
        } else {
          setIsUpgradeDialogOpen(true);
          return;
        }
      }

      // 导出音频文件
      if (audioEnabled) {
        trackEvent("export_click", {
          format: "mp3",
          type: "audio",
        });

        await exportAudioFile(fileId, "mp3");
      }

      // 关闭对话框
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  const exportTranscriptFile = async (fileId, format, options = {}) => {
    try {
      let response;

      // 注意：当前 API 只接受 fileId 和 format 两个参数
      // 新的选项参数需要后端支持后才能传递
      if (isSharedPage) {
        response = await shareService.exportShareTranscription(fileId, format);
      } else {
        response = await transcriptionService.exportTranscription(fileId, format);
      }

      if (response.status === 200) {
        handleFileDownload(
          response.data,
          response.headers,
          `exported_file.${format}`
        );

        // 追踪成功导出事件
        trackEvent("export_success", {
          format,
          type: "transcript",
          ...options,
        });
      } else {
        throw new Error("Export transcript failed");
      }
    } catch (error) {
      // 追踪导出失败事件
      trackEvent("export_error", {
        fileId,
        format,
        type: "transcript",
        error: error?.message || String(error),
      });
      console.error(error);
    }
  };

  const exportAudioFile = async (fileId, format) => {
    try {
      let response;

      // 注意：音频导出功能需要后端新增 API 支持
      // 暂时使用转录导出接口，后续需要调整为专门的音频导出接口
      if (isSharedPage) {
        response = await shareService.exportShareTranscription(fileId, format);
      } else {
        response = await transcriptionService.exportTranscription(fileId, format);
      }

      if (response.status === 200) {
        handleFileDownload(
          response.data,
          response.headers,
          `exported_audio.${format}`
        );

        // 追踪成功导出事件
        trackEvent("export_success", {
          format,
          type: "audio",
        });
      } else {
        throw new Error("Export audio failed");
      }
    } catch (error) {
      // 追踪导出失败事件
      trackEvent("export_error", {
        fileId,
        format,
        type: "audio",
        error: error?.message || String(error),
      });
      console.error(error);
    }
  };

  const handleTriggerClick = (e) => {
    if (isAnonymous) {
      e.preventDefault(); // 阻止 Dialog 打开
      trackAnonymousEvent("signin_click", {
        source: "export_file_menu",
      });
      router.push("/auth/signin");
    }
  };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <DialogTrigger asChild onClick={handleTriggerClick}>
                  <Button
                    variant="outline"
                    className={`flex items-center ${isTranscriptionComplete ? "bg-custom-bg text-white hover:bg-custom-bg-600 hover:text-white focus:bg-custom-bg-700 focus:text-white" : "bg-gray-300 text-gray-600 cursor-not-allowed"}`}
                    size={isMobile ? "icon" : "default"}
                    disabled={!isTranscriptionComplete || isAnonymous}
                  >
                    <Download className="w-4 h-4" />
                    {!isMobile && <span className="ml-2">{t("transcript")}</span>}
                  </Button>
                </DialogTrigger>
              </div>
            </TooltipTrigger>
            {!isTranscriptionComplete && (
              <TooltipContent>
                <p className="text-sm">{t("processing_export_disabled")}</p>
              </TooltipContent>
            )}
            {isAnonymous && (
              <TooltipContent>
                <p className="text-sm">{t("anonymous_export_disabled")}</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>

        <DialogContent className="sm:max-w-[500px] p-0">
          <DialogHeader className="p-6 pb-4">
            <DialogTitle className="text-xl font-semibold">{t("dialog_title")}</DialogTitle>
            <DialogDescription>{t("dialog_description")}</DialogDescription>
          </DialogHeader>

          <div className="px-6 space-y-6">
            {/* 不完整内容警告 */}
            {hasIncompleteContent && (
              <div className="px-3 py-2 mb-1 bg-amber-50 text-amber-700 text-xs rounded flex items-center whitespace-normal max-w-[300px] overflow-wrap-anywhere">
                <Info className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                <span>
                  {t("incomplete_export_warning", {
                    minutes: insufficientMinutes,
                  })}
                </span>
              </div>
            )}

            {/* Transcript Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-base font-medium">{t("transcript_section_title")}</Label>
                  <p className="text-sm text-muted-foreground">{t("transcript_section_description")}</p>
                </div>
                <Switch checked={transcriptEnabled} onCheckedChange={setTranscriptEnabled} />
              </div>

              {transcriptEnabled && (
                <div className="space-y-4 pl-4">
                  <div className="space-y-2">
                    <Label htmlFor="transcript-format" className="text-sm font-medium">
                      {t("file_format_label")}
                    </Label>
                    <Select value={transcriptFormat} onValueChange={setTranscriptFormat}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {transcriptFormats.map((format) => (
                          <SelectItem key={format.value} value={format.value}>
                            <div className="flex items-center gap-2">
                              <span>{format.label}</span>
                              {format.premium && (
                                <Badge variant="secondary" className="text-xs">
                                  <Gem className="w-3 h-3 mr-1" />
                                  {t("pro_badge")}
                                </Badge>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">{t("export_options_label")}</Label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="show-speaker" checked={showSpeaker} onCheckedChange={setShowSpeaker} />
                        <Label htmlFor="show-speaker" className="text-sm">
                          {t("show_speaker_names")}
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="show-timestamp" checked={showTimestamp} onCheckedChange={setShowTimestamp} />
                        <Label htmlFor="show-timestamp" className="text-sm">
                          {t("show_timestamps")}
                        </Label>
                      </div>
                    </div>
                  </div>

                  {/* 字幕格式警告 */}
                  {selectedTranscriptFormat?.needsWarning && (
                    <div className="px-3 py-2 bg-amber-50 text-amber-700 text-xs rounded flex items-center">
                      <Info className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                      <span>{t("subtitle_warning")}</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Audio Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-base font-medium">{t("audio_section_title")}</Label>
                  <p className="text-sm text-muted-foreground">{t("audio_section_description")}</p>
                </div>
                <Switch checked={audioEnabled} onCheckedChange={setAudioEnabled} />
              </div>

              {audioEnabled && (
                <div className="space-y-4 pl-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">{t("file_format_label")}</Label>
                    <div className="px-3 py-2 bg-gray-50 rounded-md border text-sm">MP3 (.mp3)</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 pt-4 border-t bg-gray-50/50">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {isPremiumFormat && (
                <>
                  <Gem className="w-4 h-4 text-amber-500" />
                  <span>{t("premium_format_selected")}</span>
                </>
              )}
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                {t("cancel")}
              </Button>
              <Button
                className="bg-custom-bg hover:bg-custom-bg/90"
                disabled={!transcriptEnabled && !audioEnabled}
                onClick={handleExport}
              >
                <Download className="w-4 h-4 mr-2" />
                {t("export")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <UpgradeDialog
        isOpen={isUpgradeDialogOpen}
        onClose={() => setIsUpgradeDialogOpen(false)}
        title="Upgrade Now"
        source="export_file_menu"
      />
    </>
  );
}
