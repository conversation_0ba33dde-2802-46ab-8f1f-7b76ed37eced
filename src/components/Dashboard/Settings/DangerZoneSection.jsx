"use client"

import { useTranslations } from "next-intl"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import DeleteAccountDialog from "./DeleteAccountDialog"

export default function DangerZoneSection() {
    const t = useTranslations("settings.dangerZone");
    return (
        <Card className="overflow-hidden border-destructive/20" id="danger">
            <CardHeader className="bg-red-50">
                <CardTitle className="text-destructive text-xl">{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
                <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div className="space-y-1">
                        <p className="font-medium">{t("deleteAccount.label")}</p>
                        <p className="text-sm text-muted-foreground">{t("deleteAccount.description")}</p>
                    </div>
                    <DeleteAccountDialog>
                        <Button
                            variant="destructive"
                            className="bg-red-600 hover:bg-red-700"
                        >
                            <span>{t("deleteAccount.button")}</span>
                        </Button>
                    </DeleteAccountDialog>
                </div>
            </CardContent>
        </Card>
    )
}
