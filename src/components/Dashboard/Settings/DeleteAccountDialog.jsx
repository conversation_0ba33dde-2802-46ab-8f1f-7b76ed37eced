"use client"

import { useState, useEffect } from "react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"
import { AlertTriangle } from "lucide-react"
import { useRouter } from "next/navigation"

import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { userService } from "@/services/api/userService"
import supabase from "@/lib/supabaseClient"

export default function DeleteAccountDialog({ children }) {
  const t = useTranslations("settings.dangerZone.deleteAccount.dialog");
  const [isDeleting, setIsDeleting] = useState(false)
  const [confirmText, setConfirmText] = useState("")
  const [isConfirmValid, setIsConfirmValid] = useState(false)
  const router = useRouter()

  // Check if the confirmation text matches "DELETE"
  const handleConfirmTextChange = (e) => {
    const value = e.target.value.toUpperCase()
    setConfirmText(value)
    setIsConfirmValid(value === "DELETE")
  }

  const handleDelete = async () => {
    if (!isConfirmValid) return

    setIsDeleting(true)

    try {
      // Call API to delete account
      const response = await userService.deactivateUser()

      if (response.status !== 200) {
        throw new Error("Failed to delete account")
      }

      // Show success toast
      toast.success(t("successMessage"))

      // Sign out the user
      await supabase.auth.signOut()

      // Redirect to home page
      router.push("/")
    } catch (error) {
      console.error("Failed to delete account:", error)
      if (error?.data?.message) {
        toast.error(error.data.message)
      } else {
        toast.error(t("errorMessage"))
      }
      setIsDeleting(false)
    }
  }

  const [open, setOpen] = useState(false)

  // Reset confirmation text when dialog opens/closes
  const handleOpenChange = (newOpenState) => {
    setOpen(newOpenState)
    if (newOpenState === false) {
      // Reset when dialog closes
      setConfirmText("")
      setIsConfirmValid(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader className="space-y-3">
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <DialogTitle className="text-xl font-bold text-destructive">{t("title")}</DialogTitle>
          </div>
          <DialogDescription className="text-base font-medium text-foreground">
            {t("description")} <span className="font-semibold text-destructive">{t("warning")}</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <p className="text-sm font-medium text-muted-foreground">{t("consequences.title")}</p>

          <ul className="space-y-2.5 text-sm text-muted-foreground">
            <li className="flex items-start gap-2">
              <span className="mt-0.5 h-1.5 w-1.5 rounded-full bg-muted-foreground/70"></span>
              <span>{t("consequences.items.0")}</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="mt-0.5 h-1.5 w-1.5 rounded-full bg-muted-foreground/70"></span>
              <span>{t("consequences.items.1")}</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="mt-0.5 h-1.5 w-1.5 rounded-full bg-muted-foreground/70"></span>
              <span>{t("consequences.items.2")}</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="mt-0.5 h-1.5 w-1.5 rounded-full bg-muted-foreground/70"></span>
              <span>{t("consequences.items.3")}</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="mt-0.5 h-1.5 w-1.5 rounded-full bg-muted-foreground/70"></span>
              <span>{t("consequences.items.4")}</span>
            </li>
          </ul>

          <div className="space-y-2 pt-4">
            <Label htmlFor="confirm-text" className="text-sm font-medium text-muted-foreground">
              {t("confirmation.label")}
            </Label>
            <Input
              id="confirm-text"
              type="text"
              value={confirmText}
              onChange={handleConfirmTextChange}
              placeholder={t("confirmation.placeholder")}
              className="focus-visible:ring-destructive/30 focus-visible:border-destructive/30"
              disabled={isDeleting}
            />
          </div>
        </div>

        <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:justify-end">
          <Button variant="outline" className="w-full sm:w-auto" onClick={() => handleOpenChange(false)}>
            {t("buttons.cancel")}
          </Button>
          <Button
            variant="destructive"
            className="w-full sm:w-auto"
            onClick={handleDelete}
            disabled={isDeleting || !isConfirmValid}
          >
            {isDeleting ? t("buttons.deleting") : t("buttons.delete")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
