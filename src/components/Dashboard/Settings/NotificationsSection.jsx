"use client"

import { useState, useEffect, useCallback } from "react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"

import { userService } from "@/services/api/userService"

export default function NotificationsSection({ user }) {
    const t = useTranslations("settings.notifications");
    // Notification settings state - read from user preferences
    const [notifyTranscriptionSuccess, setNotifyTranscriptionSuccess] = useState(false)
    const [notifyQuotaReset, setNotifyQuotaReset] = useState(false)
    const [notifyProductUpdates, setNotifyProductUpdates] = useState(false)

    // Initialize notification settings from user preferences
    useEffect(() => {
        if (user?.preferences) {
            setNotifyTranscriptionSuccess(user.preferences.notifyTranscriptionSuccess || false)
            setNotifyQuotaReset(user.preferences.notifyQuotaReset || false)
            setNotifyProductUpdates(user.preferences.notifyProductUpdates || false)
        }
    }, [user])

    // Save notification preference
    const handleSaveNotification = useCallback(async (type, value) => {
        try {
            // Build preferences object based on notification type
            const preferences = {};

            if (type === 'transcriptionSuccess') {
                preferences.notifyTranscriptionSuccess = value;
            } else if (type === 'quotaReset') {
                preferences.notifyQuotaReset = value;
            } else if (type === 'productUpdates') {
                preferences.notifyProductUpdates = value;
            }

            // Build user data object with only the specific notification preference
            const userData = {
                preferences
            };

            console.log("Saving notification preference:", userData);

            // Call API to save user preferences
            const response = await userService.updateUser(userData);

            if (response.status !== 200) {
                throw new Error("Failed to update notification settings");
            }

            // Show success toast
            toast.success(t("successMessage"));
        } catch (error) {
            console.error("Failed to save notification settings:", error);
            toast.error(t("errorMessage"));
        }
    }, []);

    return (
        <Card className="overflow-hidden" id="notifications">
            <CardHeader className="bg-muted/50">
                <CardTitle className="text-xl">{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
                <div className="space-y-4">
                    <div className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                            <label className="text-sm font-medium">{t("transcriptionSuccess.label")}</label>
                            <p className="text-sm text-muted-foreground">
                                {t("transcriptionSuccess.description")}
                            </p>
                        </div>
                        <Switch
                            checked={notifyTranscriptionSuccess}
                            onCheckedChange={(value) => {
                                setNotifyTranscriptionSuccess(value);
                                handleSaveNotification('transcriptionSuccess', value);
                            }}
                            className="data-[state=checked]:bg-custom-bg"
                        />
                    </div>

                    <div className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                            <label className="text-sm font-medium">{t("quotaReset.label")}</label>
                            <p className="text-sm text-muted-foreground">
                                {t("quotaReset.description")}
                            </p>
                        </div>
                        <Switch
                            checked={notifyQuotaReset}
                            onCheckedChange={(value) => {
                                setNotifyQuotaReset(value);
                                handleSaveNotification('quotaReset', value);
                            }}
                            className="data-[state=checked]:bg-custom-bg"
                        />
                    </div>

                    <div className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                            <label className="text-sm font-medium">{t("productUpdates.label")}</label>
                            <p className="text-sm text-muted-foreground">
                                {t("productUpdates.description")}
                            </p>
                        </div>
                        <Switch
                            checked={notifyProductUpdates}
                            onCheckedChange={(value) => {
                                setNotifyProductUpdates(value);
                                handleSaveNotification('productUpdates', value);
                            }}
                            className="data-[state=checked]:bg-custom-bg"
                        />
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
