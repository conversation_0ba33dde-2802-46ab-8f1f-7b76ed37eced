"use client"

import { useState, useEffect, useCallback } from "react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"
import { Clock, Globe } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"

import { userService } from "@/services/api/userService"
import LanguageSwitcher from "@/components/LanguageSwitcher"
import {
    getTimezoneDisplay,
    getInitialTimezone,
    initializeTimezones,
    getTimezonesList
} from "./TimezoneUtils"

export default function PreferencesSection({ user }) {
    const t = useTranslations("settings.preferences");

    // Timezone setting - initialize with empty string for SSR
    const [timezone, setTimezone] = useState("")

    // State to track if component is mounted (client-side)
    const [isMounted, setIsMounted] = useState(false)

    // Save timezone preference
    const handleSaveTimezone = useCallback(async (newTimezone, showToast = true) => {
        try {
            // Save timezone to localStorage for client-side use
            localStorage.setItem('uniscribe.timezone', newTimezone);

            // Build user data object with only timezone preference
            const userData = {
                preferences: {
                    timezone: newTimezone
                }
            };

            // Call API to save user preferences
            const response = await userService.updateUser(userData);

            if (response.status !== 200) {
                throw new Error("Failed to update timezone");
            }

            // Show success toast only if showToast is true (user-initiated action)
            if (showToast) {
                toast.success(t("timezone.successMessage"));
            }
        } catch (error) {
            console.error("Failed to save timezone:", error);
            // Show error toast only if showToast is true (user-initiated action)
            if (showToast) {
                toast.error(t("timezone.errorMessage"));
            }
        }
    }, []);

    // Initialize timezone and timezone list after component mounts (client-side only)
    useEffect(() => {
        // Mark component as mounted
        setIsMounted(true)

        // Update timezone list with all available timezones
        initializeTimezones()

        // Update timezone from localStorage or browser detection
        const initialTimezone = getInitialTimezone()
        setTimezone(initialTimezone)

        // If user has set a timezone, use it
        if (user?.preferences?.timezone) {
            setTimezone(user.preferences.timezone)
        }
        // If backend timezone is null but we have a browser-detected timezone, auto-save it
        else if (user?.preferences && user.preferences.timezone === null && initialTimezone) {
            // Auto-save the detected timezone to backend without showing toast notification
            handleSaveTimezone(initialTimezone, false)
        }
    }, [user, handleSaveTimezone]);

    return (
        <Card className="overflow-hidden" id="preferences">
            <CardHeader className="bg-muted/50">
                <CardTitle className="text-xl">{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
                <div className="grid gap-6 md:grid-cols-2">
                    <div className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                            <div className="flex items-center gap-2">
                                <Globe className="h-4 w-4 text-muted-foreground" />
                                <label className="text-sm font-medium">{t("language.label")}</label>
                            </div>
                            <p className="text-sm text-muted-foreground">{t("language.description")}</p>
                        </div>
                        <LanguageSwitcher />
                    </div>

                    <div className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                            <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <label className="text-sm font-medium">{t("timezone.label")}</label>
                            </div>
                            <p className="text-sm text-muted-foreground">{t("timezone.description")}</p>
                        </div>
                        <div className="flex flex-col space-y-2">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" className="ml-auto">
                                        {isMounted ? getTimezoneDisplay(timezone) : t("timezone.placeholder")}
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-[280px] max-h-[300px] overflow-y-auto">
                                    <div className="p-2">
                                        <Input
                                            type="text"
                                            placeholder={t("timezone.searchPlaceholder")}
                                            className="focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                                            onChange={(e) => {
                                                const searchBox = e.target;
                                                const searchTerm = e.target.value.toLowerCase();

                                                // Find all timezone items
                                                const items = searchBox.closest('.overflow-y-auto').querySelectorAll('[data-timezone-item]');

                                                // Filter items based on search term
                                                items.forEach(item => {
                                                    const text = item.textContent.toLowerCase();
                                                    if (text.includes(searchTerm)) {
                                                        item.style.display = 'block';
                                                    } else {
                                                        item.style.display = 'none';
                                                    }
                                                });
                                            }}
                                        />
                                    </div>

                                    <div className="py-1">
                                        {isMounted && getTimezonesList().length > 0 ? (
                                            getTimezonesList().map((tz) => (
                                                <DropdownMenuItem
                                                    key={tz.value}
                                                    onClick={() => {
                                                        setTimezone(tz.value);
                                                        handleSaveTimezone(tz.value, true);
                                                    }}
                                                    className={timezone === tz.value ? "bg-custom-bg-50 text-custom-bg" : ""}
                                                    data-timezone-item
                                                >
                                                    {tz.label}
                                                </DropdownMenuItem>
                                            ))
                                        ) : (
                                            <div className="px-2 py-1 text-sm text-gray-500">
                                                {t("timezone.loading")}
                                            </div>
                                        )}
                                    </div>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
