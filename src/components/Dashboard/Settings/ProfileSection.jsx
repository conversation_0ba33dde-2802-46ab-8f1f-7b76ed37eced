"use client"

import { useState, useEffect, useCallback } from "react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"
import { User, Mail, Save } from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"

import { userService } from "@/services/api/userService"

export default function ProfileSection({ user }) {
    const t = useTranslations("settings.profile");
    // 从用户名中提取姓和名
    const [firstName, setFirstName] = useState("")
    const [lastName, setLastName] = useState("")
    const [email, setEmail] = useState("")

    // Profile save state
    const [isSavingProfile, setIsSavingProfile] = useState(false)
    const [profileSaveSuccess, setProfileSaveSuccess] = useState(false)

    // Initialize user data when component mounts or user changes
    useEffect(() => {
        if (user) {
            // 直接使用后端返回的 firstName 和 lastName 字段
            setFirstName(user.firstName || "")
            setLastName(user.lastName || "")

            // Set email
            setEmail(user.email || "")
        }
    }, [user])

    // Save profile information
    const handleSaveProfile = useCallback(async () => {
        setIsSavingProfile(true)
        setProfileSaveSuccess(false)

        try {
            // Build user data object with only profile information
            const userData = {
                firstName,
                lastName,
                preferences: {} // Empty preferences to avoid overwriting existing ones
            };

            console.log("Saving profile data:", userData);

            // Call API to save user data
            const response = await userService.updateUser(userData);

            if (response.status !== 200) {
                throw new Error("Failed to update profile information");
            }

            // Show success toast
            toast.success(t("successMessage"));

            // Update success state
            setProfileSaveSuccess(true);

            // Clear success state after 3 seconds
            setTimeout(() => {
                setProfileSaveSuccess(false);
            }, 3000);
        } catch (error) {
            console.error("Failed to save profile:", error);
            toast.error(t("errorMessage"));
        } finally {
            setIsSavingProfile(false);
        }
    }, [firstName, lastName]);

    return (
        <Card className="overflow-hidden" id="profile">
            <CardHeader className="bg-muted/50">
                <CardTitle className="text-xl">{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
                <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                        <Avatar className="h-20 w-20">
                            <AvatarImage src={user?.avatarUrl} />
                            <AvatarFallback>
                                {firstName && lastName
                                    ? `${firstName[0]}${lastName[0]}`
                                    : firstName
                                        ? firstName[0]
                                        : "U"}
                            </AvatarFallback>
                        </Avatar>
                        <div className="space-y-1">
                            <h3 className="text-lg font-medium">
                                {firstName} {lastName}
                            </h3>
                        </div>
                    </div>

                    <Separator />

                    <div className="grid gap-6 md:grid-cols-2">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-muted-foreground" />
                                    <label className="text-sm font-medium">{t("firstName")}</label>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Input
                                        type="text"
                                        value={firstName}
                                        onChange={(e) => setFirstName(e.target.value)}
                                        className="focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-muted-foreground" />
                                    <label className="text-sm font-medium">{t("lastName")}</label>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Input
                                        type="text"
                                        value={lastName}
                                        onChange={(e) => setLastName(e.target.value)}
                                        className="focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-4">
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4 text-muted-foreground" />
                                <label className="text-sm font-medium">{t("emailAddress")}</label>
                            </div>
                            <div className="flex items-center gap-2">
                                <Input
                                    type="email"
                                    value={email}
                                    readOnly
                                    className="bg-custom-bg/5 focus-visible:ring-custom-bg focus-visible:border-custom-bg"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 flex justify-end">
                        <Button
                            className="bg-custom-bg hover:bg-custom-bg-600"
                            onClick={handleSaveProfile}
                            disabled={isSavingProfile}
                        >
                            {isSavingProfile ? (
                                <span className="flex items-center gap-2">
                                    {t("saving")}
                                </span>
                            ) : profileSaveSuccess ? (
                                <span className="flex items-center gap-2">
                                    {t("saved")}
                                </span>
                            ) : (
                                <span className="flex items-center gap-2">
                                    <Save className="h-4 w-4" /> {t("save")}
                                </span>
                            )}
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
