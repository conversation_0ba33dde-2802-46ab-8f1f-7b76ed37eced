"use client"

// Initialize with empty array (will be populated on client-side)
let TIMEZONES = [];

// Get all supported timezones from Intl API (client-side only)
export const getTimezones = () => {
    if (typeof window === 'undefined') return [];

    try {
        // Get all supported timezones
        const timezoneValues = Intl.supportedValuesOf('timeZone');

        // Format each timezone with its name
        return timezoneValues.map(tz => {
            return {
                value: tz,
                label: tz.replace(/_/g, ' ')
            };
        }).sort((a, b) => a.label.localeCompare(b.label));
    } catch (error) {
        console.error("Failed to get timezones:", error);
        return [{ value: "UTC", label: "UTC" }];
    }
};

// Get timezone display name
export const getTimezoneDisplay = (timezoneValue) => {
    if (TIMEZONES.length === 0) return timezoneValue || "UTC";
    const timezone = TIMEZONES.find(tz => tz.value === timezoneValue);
    return timezone ? timezone.label : (timezoneValue || "UTC");
}

// Detect user's timezone
export const detectUserTimezone = () => {
    try {
        return Intl.DateTimeFormat().resolvedOptions().timeZone
    } catch (error) {
        console.error("Failed to detect timezone:", error)
        return "UTC"
    }
}

// Get timezone from localStorage or detect it
export const getInitialTimezone = () => {
    try {
        const storedTimezone = localStorage.getItem('uniscribe.timezone')
        return storedTimezone || detectUserTimezone()
    } catch (error) {
        console.error("Failed to get timezone from localStorage:", error)
        return detectUserTimezone()
    }
}

// Initialize timezones list
export const initializeTimezones = () => {
    TIMEZONES = getTimezones();
    return TIMEZONES;
}

// Get the current timezones list
export const getTimezonesList = () => {
    return TIMEZONES;
}

// Format date according to user's timezone
// dateString should be in ISO 8601 format (e.g., "2025-05-15T23:05:32Z")
export const formatDateWithTimezone = (dateString, options = {}) => {
    try {
        // If dateString is not provided or invalid, return empty string
        if (!dateString || isNaN(new Date(dateString).getTime())) {
            return '';
        }

        // Get user's timezone from localStorage or use browser's timezone as fallback
        const userTimezone = getInitialTimezone();

        // Parse the date string
        // Since backend returns ISO 8601 format, we can directly create a Date object
        const dateToFormat = new Date(dateString);

        // Default formatting options
        const defaultOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: userTimezone,
            hour12: false
        };

        // Merge default options with user-provided options
        const formattingOptions = { ...defaultOptions, ...options };

        // Format the date according to user's timezone
        return new Intl.DateTimeFormat('default', formattingOptions).format(dateToFormat);
    } catch (error) {
        console.error('Error formatting date with timezone:', error);
        return dateString; // Return original string if formatting fails
    }
}
