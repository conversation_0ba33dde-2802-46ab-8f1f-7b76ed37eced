"use client";

import { useState } from "react";
import { Info } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";

export function ServiceProviderDebug({ requestedProvider, actualProvider }) {
  const [isVisible, setIsVisible] = useState(true);

  // Check if we should show the debug info
  // Show in development mode or if explicitly enabled in .env
  const shouldShow =
    process.env.NODE_ENV === "development" ||
    process.env.NEXT_PUBLIC_SHOW_SERVICE_PROVIDER === "true";

  if (!shouldShow || !isVisible) {
    return null;
  }

  // If either provider is missing, don't show anything
  if (!requestedProvider && !actualProvider) {
    return null;
  }

  // Determine if there's a mismatch between requested and actual providers
  const hasMismatch = requestedProvider && actualProvider && requestedProvider !== actualProvider;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={`flex items-center text-xs px-3 py-1 rounded-full cursor-pointer ${hasMismatch ? "bg-amber-100 text-amber-800" : "bg-gray-100 text-gray-600"
              }`}
            onClick={() => setIsVisible(false)}
          >
            <Info className="h-3 w-3 mr-1.5" />
            <span className="font-mono whitespace-nowrap">
              {requestedProvider || "unknown"}
              {hasMismatch ? " ≠ " : " = "}
              {actualProvider || "unknown"}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <div className="space-y-1 max-w-xs">
            <p className="text-xs font-semibold">Service Provider Debug Info</p>
            <p className="text-xs">
              <span className="font-semibold">Requested:</span> {requestedProvider || "unknown"}
            </p>
            <p className="text-xs">
              <span className="font-semibold">Actual:</span> {actualProvider || "unknown"}
            </p>
            {hasMismatch && (
              <p className="text-xs text-amber-600 font-semibold">
                Note: There is a mismatch between requested and actual providers
              </p>
            )}
            <p className="text-xs text-gray-500 italic mt-1">
              Click to dismiss this debug info
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
