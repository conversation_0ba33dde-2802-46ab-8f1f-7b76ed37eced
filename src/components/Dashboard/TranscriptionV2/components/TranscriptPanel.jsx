"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import {
  CircleUserRound,
  Play,
  Pause,
  Edit,
  CheckCircle,
  X,
} from "lucide-react";
import { formatDuration } from "@/lib/utils";
import TaskStatusHandler from "@/components/Dashboard/TaskStatusHandler";
import { useTranslations } from "next-intl";
import { TASK_STATUS } from "@/constants/task";
import { Button } from "@/components/ui/button";
import { useAuthStore } from "@/stores/useAuthStore";
import { useRouter } from "@/i18n/navigation";
import UpgradeDialog from "@/components/Dashboard/UpgradeDialog";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { transcriptionService } from "@/services/api/transcriptionService";
import { trackAnonymousEvent, trackEvent } from "@/lib/analytics";
import { ToastContainer } from "@/components/ui/toast";
import { useMediaQuery } from "@/hooks/useMediaQuery";

const PREVIEW_MINUTES = 2;
const PREVIEW_TIME_LIMIT = PREVIEW_MINUTES * 60; // Convert to seconds

export function TranscriptPanel({
  segments,
  currentSegment,
  onSegmentClick,
  taskStatuses,
  taskErrors,
  playerRef,
  isAnonymous,
  insufficientMinutes = 0,
  totalDuration = 0,
  fileId,
  sourceType,
  sourceUrl, // Kept for future use if needed to display source URL
  isEditingSegment,
  setIsEditingSegment,
}) {
  const t = useTranslations("transcription");
  const containerRef = useRef(null);
  const segmentRefs = useRef({});
  const { user } = useAuthStore();
  const router = useRouter();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const { summary } = useEntitlementsStore();

  // 编辑模式状态
  const [editingSegmentId, setEditingSegmentId] = useState(null);
  const [editedText, setEditedText] = useState("");
  const textareaRefs = useRef({});

  // 移动端检测，需在所有用到 isMobile 的地方之前
  const isMobile = useMediaQuery("(max-width: 768px)");

  // 同步编辑状态到父组件
  useEffect(() => {
    setIsEditingSegment(editingSegmentId !== null);
  }, [editingSegmentId, setIsEditingSegment]);

  // 跟踪播放状态
  const [isPlaying, setIsPlaying] = useState(false);

  // 跟踪鼠标悬停的 segment
  const [hoveredSegmentId, setHoveredSegmentId] = useState(null);

  // Toast 状态
  const [toast, setToast] = useState(null);

  // 处理Toast自动消失
  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => {
        setToast(null);
      }, toast.duration || 2000);

      return () => clearTimeout(timer);
    }
  }, [toast]);

  // 监听播放器状态变化
  useEffect(() => {
    const checkPlayingStatus = () => {
      if (playerRef.current) {
        setIsPlaying(playerRef.current.isPlaying());
      }
    };

    // 每秒检查一次播放状态
    const interval = setInterval(checkPlayingStatus, 1000);

    return () => clearInterval(interval);
  }, [playerRef]);

  const getMaxViewableTime = () => {
    if (isAnonymous) {
      return PREVIEW_TIME_LIMIT;
    } else if (insufficientMinutes > 0) {
      return totalDuration - insufficientMinutes * 60;
    }
    return Infinity;
  };

  const maxViewableTime = getMaxViewableTime();

  const isSegmentViewable = (segment) => segment.end <= maxViewableTime;

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingSegmentId(null);
    setEditedText("");
  }, []);

  // 用于防止重复保存的标志
  const isSavingRef = useRef(false);

  // 保存编辑的文本
  const handleSaveEdit = useCallback(
    async (segment) => {
      // 如果已经在保存中，直接返回
      if (isSavingRef.current) {
        return;
      }

      // 如果文本没有变化，直接退出编辑模式，不发送请求
      if (editedText === segment.text) {
        setEditingSegmentId(null);
        return;
      }

      // 设置保存中标志
      isSavingRef.current = true;

      try {
        const response = await transcriptionService.updateSegment(
          fileId,
          segment.id,
          editedText
        );

        if (response.data && response.data.success) {
          // 更新本地段落数据
          const updatedSegment = response.data.segment;

          // 通知父组件更新段落数据
          if (onSegmentClick && updatedSegment) {
            onSegmentClick(updatedSegment);
          }

          // 记录保存成功事件
          trackEvent("transcript_edit_save", {
            success: true,
          });

          // 显示保存成功提示
          setToast({
            message: t("transcript.saveSuccess"),
            variant: "success",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("Error updating segment:", error);

        // 记录保存失败事件
        trackEvent("transcript_edit_save", {
          success: false,
          error: error.message || "Unknown error",
        });

        // 显示保存失败提示
        setToast({
          message: t("transcript.saveError"),
          variant: "error",
          duration: 3000,
        });
      } finally {
        // 重置保存中标志
        isSavingRef.current = false;

        // 退出编辑模式
        setEditingSegmentId(null);
      }
    },
    [editedText, fileId, onSegmentClick, t]
  );

  // 处理文本变化
  const handleTextChange = useCallback((e) => {
    setEditedText(e.target.value);

    // 自动调整 textarea 高度
    const textarea = e.target;
    textarea.style.height = "auto";
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, []);

  // 处理单击事件 - 选择段落并跳转到对应时间点，但保持当前播放/暂停状态
  const handleSegmentClick = useCallback(
    (segment, skipSeek = false) => {
      if (!isSegmentViewable(segment)) {
        return;
      }

      // 如果当前正在编辑，不做任何操作
      if (editingSegmentId !== null) {
        return;
      }

      // 检查是否有任何文本输入元素处于活动状态（编辑模式）
      const isEditingText =
        document.activeElement.tagName === "TEXTAREA" ||
        document.activeElement.tagName === "INPUT" ||
        document.activeElement.isContentEditable;

      // 如果用户正在编辑文本，不做任何操作
      if (isEditingText) {
        return;
      }

      // 判断是否是当前选中的 segment
      const isCurrentSegmentClick = currentSegment?.id === segment.id;

      // 选择段落 - 只有在不是当前选中的 segment 时才更新
      if (!isCurrentSegmentClick) {
        onSegmentClick(segment);
      }

      // 跳转到对应时间点，但保持当前播放/暂停状态
      // 以下情况不跳转：
      // 1. skipSeek 为 true（用于编辑模式）
      // 2. 点击的是当前选中的 segment（无论是否正在播放）
      if (playerRef.current && !skipSeek && !isCurrentSegmentClick) {
        playerRef.current.seekTo(segment.start);
        // 不调用 play() 或 pause()，保持当前状态
      }
    },
    [
      isSegmentViewable,
      editingSegmentId,
      onSegmentClick,
      playerRef,
      currentSegment,
    ]
  );

  // 处理播放按钮点击
  const handlePlayClick = useCallback(
    (e, segment) => {
      e.stopPropagation(); // 阻止事件冒泡

      if (!isSegmentViewable(segment)) {
        return;
      }

      onSegmentClick(segment);
      if (playerRef.current) {
        // 如果点击的是当前正在播放的段落，则切换播放/暂停状态
        if (currentSegment?.id === segment.id && isPlaying) {
          playerRef.current.pause();
        } else {
          // 否则，跳转到该段落并开始播放
          playerRef.current.seekTo(segment.start);
          playerRef.current.play();
        }
      }
    },
    [isSegmentViewable, onSegmentClick, playerRef, currentSegment, isPlaying]
  );

  // 处理编辑按钮点击
  const handleEditClick = useCallback(
    (e, segment, source = "button") => {
      if (isMobile) return; // 移动端禁用编辑入口
      e.stopPropagation(); // 阻止事件冒泡

      if (!isSegmentViewable(segment)) {
        return;
      }

      // 记录进入编辑状态的事件
      trackEvent("transcript_edit_start", {
        source: source, // 'button' 或 'double_click'
      });

      // 如果不是当前选中的段落，先选中它并跳转到对应时间点
      if (currentSegment?.id !== segment.id) {
        onSegmentClick(segment); // 更新当前选中的 segment

        // 跳转到对应时间点
        if (playerRef.current) {
          playerRef.current.seekTo(segment.start);
        }
      }

      setEditingSegmentId(segment.id);
      setEditedText(segment.text);

      // 使用 setTimeout 确保 DOM 已更新
      setTimeout(() => {
        if (textareaRefs.current[segment.id]) {
          const textarea = textareaRefs.current[segment.id];
          textarea.focus();

          // 调整 textarea 高度以匹配内容
          textarea.style.height = "auto";
          textarea.style.height = `${textarea.scrollHeight}px`;
        }
      }, 0);
    },
    [
      isSegmentViewable,
      currentSegment,
      onSegmentClick,
      playerRef,
      fileId,
      isMobile,
    ]
  );

  useEffect(() => {
    if (!currentSegment || !segmentRefs.current[currentSegment.id]) {
      return;
    }

    segmentRefs.current[currentSegment.id].scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }, [currentSegment]);

  // 处理按下 Escape 键取消编辑和 Cmd/Ctrl+S 保存
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 处理 Escape 键取消编辑
      if (e.key === "Escape" && editingSegmentId !== null) {
        handleCancelEdit();
      }

      // 处理 Cmd/Ctrl+S 保存
      if (
        (e.ctrlKey || e.metaKey) &&
        e.key === "s" &&
        editingSegmentId !== null
      ) {
        e.preventDefault(); // 阻止浏览器默认的保存行为

        // 找到当前编辑的 segment
        const editingSegment = segments?.find(
          (seg) => seg.id === editingSegmentId
        );
        if (editingSegment) {
          handleSaveEdit(editingSegment);
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [editingSegmentId, handleCancelEdit, handleSaveEdit, segments]);

  // 处理点击文档其他区域时保存编辑
  useEffect(() => {
    if (editingSegmentId === null) return;

    const handleClickOutside = (e) => {
      // 检查点击是否在编辑区域外
      const isOutsideClick =
        textareaRefs.current[editingSegmentId] &&
        !textareaRefs.current[editingSegmentId].contains(e.target) &&
        !e.target.closest("button"); // 排除按钮点击

      if (isOutsideClick) {
        // 找到当前编辑的 segment
        const editingSegment = segments?.find(
          (seg) => seg.id === editingSegmentId
        );
        if (editingSegment) {
          // 使用 setTimeout 确保在其他事件处理完成后执行保存
          setTimeout(() => {
            handleSaveEdit(editingSegment);
          }, 100);
        }
      }
    };

    // 立即添加事件监听器
    document.addEventListener("mousedown", handleClickOutside);

    // 添加一个额外的 mouseup 事件监听器，以防 mousedown 事件被阻止
    document.addEventListener("mouseup", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("mouseup", handleClickOutside);
    };
  }, [editingSegmentId, segments, handleSaveEdit]);

  const getOverlayProps = () => {
    if (isAnonymous) {
      return {
        message: t("transcript.preview_limit_message", {
          minutes: PREVIEW_MINUTES,
        }),
        buttonText: t("transcript.signin_to_view_full"),
        buttonAction: () => {
          trackAnonymousEvent("signin_click", {
            source: "transcript_preview_limit",
          });
          router.push("/auth/signin");
        },
      };
    } else if (insufficientMinutes > 0) {
      // Check if user has enough remaining minutes to unlock
      const remainingMinutes = summary?.remainingCredits || 0;
      if (remainingMinutes > insufficientMinutes) {
        return {
          message: t("transcript.insufficient_minutes", {
            minutes: insufficientMinutes,
          }),
          buttonText: t("common.unlock"),
          buttonAction: async () => {
            try {
              await transcriptionService.unlockTranscription(fileId);
              // Refresh the page or update the state after successful unlock
              window.location.reload();
            } catch (error) {
              console.error("Error unlocking transcription:", error);
              // 处理错误情况
              // 如果是余额不足，显示升级对话框
              if (error.data?.code === 30006) {
                // 显示升级对话框
                setShowUpgradeDialog(true);
              } else {
                // 其他错误，可以使用浏览器的 alert 或者其他方式显示错误信息
                alert(
                  t("errors.unlock_failed", {
                    defaultValue:
                      "Failed to unlock transcription. Please try again later.",
                  })
                );
              }
            }
          },
        };
      }

      // Default upgrade case when user doesn't have enough remaining minutes
      return {
        message: t("transcript.insufficient_minutes", {
          minutes: insufficientMinutes,
        }),
        buttonText: t("common.upgrade"),
        buttonAction: () => setShowUpgradeDialog(true),
      };
    }
    return null;
  };

  const overlayProps = getOverlayProps();
  const shouldShowOverlay = isAnonymous || insufficientMinutes > 0;

  const getProcessingMessage = () => {
    // Check if this is a YouTube transcription and if it's in the download phase
    if (
      sourceType === "youtube" &&
      taskStatuses?.youtubeDownload === TASK_STATUS.PROCESSING
    ) {
      return (
        <span className="text-custom-bg font-medium">
          {t("transcript.status.youtube_downloading", {
            defaultValue: "Downloading YouTube media file...",
          })}
        </span>
      );
    }

    // Default processing messages
    if (user?.hasPaidPlan) {
      return (
        <span className="text-custom-bg font-medium">
          {t("transcript.status.premium_transcribing")}
        </span>
      );
    }
    return t("transcript.status.premium_transcribing_free_trial");
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-none px-6 pt-4 bg-gray-50 sticky top-0 z-10">
        <h2 className="text-lg font-bold">{t("transcript.title")}</h2>
      </div>

      {/* 显示Toast提示 */}
      {toast && (
        <ToastContainer
          variant={toast.variant}
          position="top"
          className="z-50 flex items-center justify-between gap-2 px-4 py-2"
        >
          <div className="flex items-center gap-2">
            {toast.variant === "success" && (
              <CheckCircle className="h-4 w-4 text-green-600" />
            )}
            {toast.variant === "error" && (
              <X className="h-4 w-4 text-red-600" />
            )}
            <span>{toast.message}</span>
          </div>
          <button
            onClick={() => setToast(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </button>
        </ToastContainer>
      )}

      <TaskStatusHandler
        status={
          // If it's a YouTube source and download is not completed, show the download status instead
          sourceType === "youtube" &&
            taskStatuses?.youtubeDownload !== TASK_STATUS.COMPLETED
            ? taskStatuses?.youtubeDownload
            : taskStatuses.transcription
        }
        error={
          // If it's a YouTube source and download failed, show the download error instead
          sourceType === "youtube" &&
            taskStatuses?.youtubeDownload === TASK_STATUS.FAILED
            ? taskErrors?.youtubeDownload || taskErrors?.transcription
            : taskErrors?.transcription
        }
        taskName={t("transcript.title")}
        message={getProcessingMessage()}
      >
        <div className="relative flex-1 min-h-0">
          <div ref={containerRef} className="h-full overflow-y-auto">
            {segments?.map((segment) => {
              const isCurrentSegment = currentSegment?.id === segment.id;

              if (!isSegmentViewable(segment)) {
                return null;
              }

              return (
                <div
                  key={segment.id}
                  ref={(el) => (segmentRefs.current[segment.id] = el)}
                  className="
                    mb-2
                    cursor-pointer
                    relative
                    rounded-lg
                  "
                  onClick={() => handleSegmentClick(segment)}
                  onMouseEnter={() => setHoveredSegmentId(segment.id)}
                  onMouseLeave={() => setHoveredSegmentId(null)}
                >
                  {/* 操作按钮 - 悬浮时显示在右上角，编辑状态下不显示 */}
                  {(hoveredSegmentId === segment.id || isCurrentSegment) &&
                    editingSegmentId !== segment.id &&
                    !isMobile && (
                      <div className="absolute top-2 right-2 flex items-center space-x-1 z-10">
                        {/* 播放按钮 */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className={`h-7 w-7 rounded-full bg-white/90 text-custom-bg hover:bg-custom-bg hover:text-white transition-all duration-150`}
                          onClick={(e) => handlePlayClick(e, segment)}
                          title={
                            isPlaying && currentSegment?.id === segment.id
                              ? t("transcript.pause")
                              : t("transcript.play")
                          }
                        >
                          {isPlaying && currentSegment?.id === segment.id ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>

                        {/* 编辑按钮 */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className={`h-7 w-7 rounded-full bg-white/90 text-custom-bg hover:bg-custom-bg hover:text-white transition-all duration-150`}
                          onClick={(e) => handleEditClick(e, segment)}
                          title={t("transcript.edit")}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    )}

                  <div className="p-4 rounded-lg">
                    {/* 发言人和时间信息 */}
                    <div className="flex items-center space-x-2 mb-3">
                      <CircleUserRound className="w-6 h-6 text-custom-bg" />
                      <span className="text-sm font-medium text-gray-500">
                        {t("transcript.speaker")}
                      </span>
                      <span className="text-sm text-gray-400">
                        {formatDuration(segment.start)} -{" "}
                        {formatDuration(segment.end)}
                      </span>

                      {/* 编辑状态提示 - 放在时间信息右侧 */}
                      {editingSegmentId === segment.id && (
                        <div className="ml-2 text-xs px-2 py-1 rounded-full bg-custom-bg/10 text-custom-bg flex items-center space-x-1">
                          <Edit className="h-3 w-3" />
                          <span>{t("transcript.editingTip")}</span>
                        </div>
                      )}
                    </div>

                    {/* 文本内容 - 编辑模式或显示模式 */}
                    {editingSegmentId === segment.id ? (
                      <div className="relative">
                        <textarea
                          ref={(el) => (textareaRefs.current[segment.id] = el)}
                          value={editedText}
                          onChange={handleTextChange}
                          className={`w-full p-3 rounded-lg focus:outline-none focus:ring-0 text-sm leading-relaxed min-h-[1.5rem] ${isCurrentSegment
                            ? "bg-custom-bg text-white border-2 border-custom-bg"
                            : "bg-white text-gray-600 border-2 border-custom-bg"
                            }`}
                          onClick={(e) => e.stopPropagation()}
                          onKeyDown={(e) => {
                            // 处理 Ctrl+Enter 或 Cmd+Enter 保存
                            if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
                              e.preventDefault();
                              handleSaveEdit(segment);
                            }
                          }}
                          onBlur={(e) => {
                            // 防止事件冒泡，确保不会触发其他点击事件
                            e.stopPropagation();

                            // 注意：我们不在这里自动保存，因为现在有明确的保存按钮
                            // 用户可能会点击取消按钮，所以我们不应该在失去焦点时自动保存
                          }}
                          style={{
                            resize: "none",
                            overflow: "hidden", // 隐藏滚动条
                            lineHeight: "inherit",
                          }}
                        />

                        {/* 保存和取消按钮 */}
                        <div className="flex justify-end mt-2 space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-gray-500 hover:text-gray-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCancelEdit();
                            }}
                          >
                            {t("transcript.cancel")}
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            className="bg-custom-bg hover:bg-custom-bg/90 text-white"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSaveEdit(segment);
                            }}
                          >
                            {t("transcript.save")}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div
                        className={`p-3 rounded-lg ${isCurrentSegment
                          ? "bg-custom-bg text-white border-2 border-custom-bg"
                          : "bg-white text-gray-600 border-2 border-transparent hover:border-custom-bg"
                          }`}
                        onClick={(e) => {
                          // 先阻止事件冒泡，避免触发外层容器的点击事件
                          e.stopPropagation();

                          // 如果不是当前选中的段落，先选中它并跳转到对应时间点
                          if (!isCurrentSegment) {
                            handleSegmentClick(segment, false); // 不跳过 seek，允许进度条跳转
                          }

                          // 单击只选择段落，不进入编辑模式
                        }}
                        onDoubleClick={(e) => {
                          if (isMobile) return; // 移动端禁用双击编辑
                          // 阻止事件冒泡
                          e.stopPropagation();

                          // 双击时进入编辑模式，传递 'double_click' 作为来源
                          handleEditClick(e, segment, "double_click");
                        }}
                      >
                        <p className="text-sm leading-relaxed min-h-[1.5rem]">
                          {segment.text}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {shouldShowOverlay && overlayProps && (
            <div className="absolute bottom-0 left-0 right-0 h-48 bg-gradient-to-t from-white via-white to-transparent pointer-events-none">
              <div className="absolute bottom-0 left-0 right-0 flex flex-col items-center justify-center p-6 pointer-events-auto">
                <p className="text-sm text-custom-bg text-center mb-4">
                  {overlayProps.message}
                </p>
                <Button
                  className="bg-custom-bg hover:bg-custom-bg/90"
                  onClick={overlayProps.buttonAction}
                >
                  {overlayProps.buttonText}
                </Button>
              </div>
            </div>
          )}
        </div>
      </TaskStatusHandler>

      {showUpgradeDialog && (
        <UpgradeDialog
          isOpen={showUpgradeDialog}
          onClose={() => setShowUpgradeDialog(false)}
          source="transcript_insufficient_minutes"
        />
      )}
    </div>
  );
}
