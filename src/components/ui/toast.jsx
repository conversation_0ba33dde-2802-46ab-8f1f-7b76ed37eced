"use client"

import * as React from "react"
import { cva } from "class-variance-authority";
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const ToastProvider = React.createContext({
  showToast: () => {},
  hideToast: () => {},
})

export const useToast = () => React.useContext(ToastProvider)

const toastVariants = cva(
  "fixed z-50 flex items-center justify-between gap-2 rounded-md border p-4 shadow-md transition-all",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        success: "bg-green-50 text-green-800 border-green-200",
        error: "bg-red-50 text-red-800 border-red-200",
        warning: "bg-yellow-50 text-yellow-800 border-yellow-200",
        info: "bg-blue-50 text-blue-600 border-blue-200",
      },
      position: {
        topRight: "top-4 right-4",
        topLeft: "top-4 left-4",
        bottomRight: "bottom-4 right-4",
        bottomLeft: "bottom-4 left-4",
        top: "top-4 left-1/2 -translate-x-1/2",
        bottom: "bottom-4 left-1/2 -translate-x-1/2",
      },
    },
    defaultVariants: {
      variant: "default",
      position: "top",
    },
  }
)

export const ToastContainer = React.forwardRef(({ className, variant, position, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(toastVariants({ variant, position }), className)}
    {...props}
  />
))
ToastContainer.displayName = "ToastContainer"

export const Toast = ({ message, variant = "default", position = "top", duration = 3000, onClose }) => {
  React.useEffect(() => {
    const timer = setTimeout(() => {
      onClose && onClose()
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  return (
    <ToastContainer variant={variant} position={position}>
      <div>{message}</div>
      <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
        <X className="h-4 w-4" />
      </button>
    </ToastContainer>
  )
}

export const ToastProviderComponent = ({ children }) => {
  const [toasts, setToasts] = React.useState([])

  const showToast = React.useCallback((message, options = {}) => {
    const id = Date.now().toString()
    setToasts((prev) => [...prev, { id, message, ...options }])
    return id
  }, [])

  const hideToast = React.useCallback((id) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  const value = React.useMemo(() => ({ showToast, hideToast }), [showToast, hideToast])

  return (
    <ToastProvider.Provider value={value}>
      {children}
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          message={toast.message}
          variant={toast.variant}
          position={toast.position}
          duration={toast.duration}
          onClose={() => hideToast(toast.id)}
        />
      ))}
    </ToastProvider.Provider>
  )
}
