const isValidYouTubeUrl = (url) => {
  // 1. 基本 URL 格式验证
  try {
    new URL(url);
  } catch (e) {
    return {
      isValid: false,
      reason: "Invalid URL format",
    };
  }

  // 2. 检查是否包含嵌套的 YouTube URL
  const extractNestedYouTubeUrl = (inputUrl) => {
    // 查找嵌套的 YouTube URL 模式
    const nestedPatterns = [
      // 匹配 youtube.com/watch?v= 格式
      {
        pattern: /https?:\/\/(?:www\.)?youtube\.com\/watch\?(?=.*v=([^&]{11,}))(?:\S+)?/g,
        idIndex: 1
      },
      // 匹配 youtu.be/ 格式
      {
        pattern: /https?:\/\/(?:www\.)?youtu\.be\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1
      },
      // 匹配 youtube.com/embed/ 格式
      {
        pattern: /https?:\/\/(?:www\.)?youtube\.com\/embed\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1
      },
      // 匹配 youtube.com/shorts/ 格式
      {
        pattern: /https?:\/\/(?:www\.)?youtube\.com\/shorts\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1
      },
      // 匹配 youtube.com/live/ 格式
      {
        pattern: /https?:\/\/(?:www\.)?youtube\.com\/live\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1
      },
    ];

    for (const { pattern, idIndex } of nestedPatterns) {
      const matches = [...inputUrl.matchAll(pattern)];
      if (matches.length > 0) {
        // 如果找到多个匹配，使用第一个
        const videoId = matches[0][idIndex];
        if (videoId && /^[A-Za-z0-9_-]{11,}$/.test(videoId)) {
          // 构造标准化的 YouTube URL
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }
    }
    return null;
  };

  // 尝试提取嵌套的 YouTube URL
  const nestedUrl = extractNestedYouTubeUrl(url);
  const urlToCheck = nestedUrl || url;

  // 3. YouTube URL 格式和 Video ID 验证
  const patterns = [
    /^(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?(?=.*v=([^&]{11,}))(?:\S+)?$/,
    /^(?:https?:\/\/)?(?:www\.)?youtu\.be\/([^?]{11,})(?:\?.*)?$/,
    /^(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([^?]{11,})(?:\?.*)?$/,
    /^(?:https?:\/\/)?(?:www\.)?youtube\.com\/shorts\/([^?]{11,})(?:\?.*)?$/,
    /^(?:https?:\/\/)?(?:www\.)?youtube\.com\/live\/([^?]{11,})(?:\?.*)?$/,
  ];

  for (const pattern of patterns) {
    const match = urlToCheck.match(pattern);
    if (match) {
      const videoId = match[1];
      // 验证 video ID 是否符合 YouTube 规则（至少11位字母数字和特定字符）
      if (/^[A-Za-z0-9_-]{11,}$/.test(videoId)) {
        return {
          isValid: true,
          videoId: videoId,
          originalUrl: url,
          extractedUrl: nestedUrl || url,
        };
      }
    }
  }

  return {
    isValid: false,
    reason: "Invalid YouTube video ID",
  };
};

export default isValidYouTubeUrl;
