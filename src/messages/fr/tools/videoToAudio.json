{"title": "Extracteur Audio à partir de Vidéo", "subtitle": "Extraire l'audio de vos vidéos et enregistrer au format MP3", "loading": "Chargement...", "features": {"secure": {"title": "100 % Sécure", "description": "Conversion basée sur le navigateur - les fichiers ne quittent jamais votre appareil"}, "fast": {"title": "Rapide et Gratuit", "description": "Rapide, privé et entièrement gratuit à utiliser"}, "noRegistration": {"title": "Pas d'enregistrement", "description": "Aucune inscription ni téléchargement sur les serveurs requis"}}, "buttons": {"selectFile": "Sélectionner le fichier vidéo", "extract": "Extraire l'audio", "extracting": "Extracting...", "download": "Télécharger MP3"}, "progress": {"extracting": "Extraction :", "completed": "Extraction terminée en {time} secondes"}, "error": {"invalidFormat": "Veuillez sélectionner un fichier vidéo valide (MP4, WebM, OGG ou MOV)", "unsupportedFormat": "Format vidéo non pris en charge", "extractionFailed": "L'extraction audio a échoué. Veuillez réessayer."}, "meta": {"title": "Extracteur de vidéo en audio gratuit basé sur le navigateur | Outils UniScribe", "description": "Extraire l'audio des fichiers vidéo en toute sécurité dans votre navigateur. Aucun téléchargement de fichier nécessaire - tout le traitement se fait localement sur votre appareil. Convertissez les vidéos au format MP3 en toute confidentialité. Rapide et gratuit.", "ogImageAlt": "Extracteur Audio de Vidéo UniScribe"}, "faq": {"extractionTime": {"question": "Combien de temps faut-il pour extraire l'audio ?", "answer": "Le temps d'extraction dépend de la taille du fichier vidéo. Pour une vidéo typique, il faut environ 30 secondes pour extraire l'audio."}, "supportedFormats": {"question": "Puis-je extraire l'audio de n'importe quel format vidéo ?", "answer": "<PERSON><PERSON>, vous pouvez extraire l'audio des vidéos aux formats MP4, WebM, OGG et MOV. Cependant, le format de l'audio de sortie sera MP3."}, "dataSecurity": {"question": "Mes données sont-elles sécurisées ?", "answer": "<PERSON><PERSON>, vos données sont sécurisées. La conversion s'effectue entièrement dans votre navigateur, et aucun fichier n'est téléchargé sur nos serveurs."}, "useCases": {"question": "Pourquoi voudrais-je extraire l'audio d'une vidéo ?", "answer": "Il existe de nombreuses raisons d'extraire l'audio des vidéos : créer des podcasts à partir de contenu vidéo, sauvegarder de la musique à partir de clips musicaux, extraire des effets sonores pour le montage, réduire la taille des fichiers lorsque seul l'audio est nécessaire, ou convertir des cours vidéo en audio pour une écoute en déplacement."}}}