{"title": "<PERSON>ideó<PERSON><PERSON><PERSON>", "subtitle": "Készítsen hangot a videóiból és mentse el MP3 formátumban.", "loading": "Betöltés...", "features": {"secure": {"title": "100% Biztonságos", "description": "Böngészőalapú konverzió - a fájlok soha nem hagyják el az eszközét"}, "fast": {"title": "Gyors és ingyenes", "description": "<PERSON><PERSON><PERSON>, privát és teljesen ingyenesen használható"}, "noRegistration": {"title": "<PERSON><PERSON><PERSON> re<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> szükség regisztrációra vagy fájlok feltöltésére a szerverekre."}}, "buttons": {"selectFile": "Videófájl kiválasztása", "extract": "<PERSON> kinyerés", "extracting": "<PERSON><PERSON><PERSON>...", "download": "MP3 letöltése"}, "progress": {"extracting": "Kivonás:", "completed": "<PERSON>z extrakció {time} másodperc alatt befejeződött."}, "error": {"invalidFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, válasszon egy érvényes videófájlt (MP4, WebM, OGG vagy MOV)", "unsupportedFormat": "Támogatott videóformátum", "extractionFailed": "A hangkivonás sikertelen. K<PERSON>rj<PERSON>k, próbá<PERSON>ja meg <PERSON>."}, "meta": {"title": "Ingyenes böngészőalapú videóból audió <PERSON> | UniScribe Eszközök", "description": "Biztonságosan kinyerheti az audiót videófájlokból a böngészőjében. Nincs szükség fájl feltöltésére - minden feldolgozás he<PERSON>, az eszközén történik. Konvertálja a videókat MP3 formátumba teljes adatvédelemmel. Gyors és ingyenes.", "ogImageAlt": "UniScribe Videó az Audio Kivonó"}, "faq": {"extractionTime": {"question": "Mennyi időbe telik az audio kinyerése?", "answer": "A kinyerési idő a videofájl méretétől függ. Egy tipikus videó esetében körülbelül 30 másodperc szükséges a hang kinyeréséhez."}, "supportedFormats": {"question": "Kihúzhatok hangot bármilyen videóformátumból?", "answer": "<PERSON><PERSON>, hangot tud kinyerni MP4, WebM, OGG és MOV formátumú videókból. Azonban a kimeneti hang formátuma MP3 lesz."}, "dataSecurity": {"question": "Biztonságos az adatom?", "answer": "<PERSON><PERSON>, az adatai biztonságban vannak. A konverzió teljes mértékben a böngészőjében történik, és egyetlen fájl sem kerül feltöltésre a szervereinkre."}, "useCases": {"question": "<PERSON><PERSON><PERSON> s<PERSON>etn<PERSON>m hangot kinyerni egy videóból?", "answer": "Számos oka van az audio videóból való kinyerésének: podcastek készítése videós tartalomból, zene mentése zenei videókból, hanghatások kinyerése szerkesztéshez, fájlm<PERSON>ret csökkentése, amikor csak audio szükséges, vagy videó előadások audio formátumra konvertálása, hogy útközben hallgathassuk."}}}