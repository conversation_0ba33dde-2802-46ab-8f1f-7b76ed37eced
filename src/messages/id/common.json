{"copyButton": {"copy": "<PERSON><PERSON>", "copied": "Di<PERSON>in"}, "faq": {"title": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "upgradeDialog": {"title": "Rencana & Harga", "description": ""}, "fileDisplay": {"duration_billing_info": "Durasi transkripsi kurang dari 1 menit akan dihitung sebagai 1 menit", "subtitle": {"generate_subtitle": "Hasilkan Subtitle", "subtitle_tooltip": "Aktifkan opsi ini untuk menghasilkan subtitle SRT/VTT"}}, "uploadPrompt": {"loggedIn": {"gotoDashboard": "<PERSON><PERSON> ke dasbor", "description": "<PERSON><PERSON> dan kelola <PERSON><PERSON>"}, "dragAndDrop": "Seret file di sini untuk mengunggah", "browseFiles": "<PERSON><PERSON><PERSON><PERSON>"}, "fileUploadStatus": {"defaultError": "<PERSON><PERSON><PERSON> masuk untuk mengunggah file yang lebih panjang dari 120 menit.", "preparing": "Mempersia<PERSON><PERSON> unggahan <PERSON>...", "processing": "Memproses: {progress}%", "uploading": "Mengunggah: {progress}%", "completing": "Menyelesaikan <PERSON> file...", "success": "<PERSON><PERSON><PERSON> Berhasil", "transcribe": "Transkripsi", "viewTranscript": "Li<PERSON>", "loginHint": "<PERSON><PERSON>, tidak perlu mengunggah ulang setelah masuk.", "error": {"transcriptionFailed": "Gagal membuat tugas transkripsi"}}, "deleteDialog": {"title": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus file ini?", "description": "Tindakan ini tidak dapat dibatalkan. Ini akan menghapus file yang Anda unggah secara permanen.", "cancel": "Batalkan", "confirm": "Hapus"}, "languageSelector": {"label": "Pilih Ba<PERSON>:", "placeholder": "<PERSON><PERSON><PERSON> bahasa", "searchPlaceholder": "<PERSON>i bahasa...", "noResults": "Tidak ada bahasa yang di<PERSON>ukan"}, "limitReachedDialog": {"title": "Batas <PERSON> Dicapai", "description": "Pengguna Free hanya dapat mentranskripsi 5 file per hari. <PERSON><PERSON><PERSON><PERSON><PERSON> atau membeli paket satu kali untuk mentranskripsi lebih banyak file.", "cancel": "<PERSON><PERSON><PERSON>", "upgrade": "Tingkatkan Sekarang"}, "taskStatus": {"waiting": "Menunggu tugas untuk mulai...", "processing": "Memproses...", "notStarted": "Belum dimulai"}}