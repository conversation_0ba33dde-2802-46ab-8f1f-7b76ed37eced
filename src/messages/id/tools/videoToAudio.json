{"title": "Ekstraktor Video ke Audio", "subtitle": "Ekstrak audio dari video Anda dan simpan sebagai MP3", "loading": "Memuat...", "features": {"secure": {"title": "100% Aman", "description": "Konversi berbasis browser - file tidak pernah meninggalkan perangkat Anda"}, "fast": {"title": "Cepat & Gratis", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan sepenuhnya gratis untuk digunakan"}, "noRegistration": {"title": "Tidak Perlu Registrasi", "description": "Tidak ada pendaftaran atau unggahan ke server yang dip<PERSON><PERSON>an"}}, "buttons": {"selectFile": "<PERSON><PERSON><PERSON>", "extract": "Ekstrak Audio", "extracting": "Mengekstrak...", "download": "Unduh MP3"}, "progress": {"extracting": "Mengekstrak:", "completed": "Ekstraksi selesai dalam {time} detik"}, "error": {"invalidFormat": "Silakan pilih file video yang valid (MP4, WebM, OGG, atau MOV)", "unsupportedFormat": "Format video tidak didukung", "extractionFailed": "Ekstraksi audio gagal. Silakan coba lagi."}, "meta": {"title": "Ekstraktor Video ke Audio Berbasis Browser Gratis | Alat UniScribe", "description": "Ekstrak audio dari file video dengan aman di browser Anda. Tidak perlu mengunggah file - semua pemrosesan terjadi secara lokal di perangkat Anda. Konversi video ke format MP3 dengan privasi penuh. Cepat dan gratis.", "ogImageAlt": "UniScribe Ekstraktor Video ke Audio"}, "faq": {"extractionTime": {"question": "<PERSON><PERSON>a lama waktu yang dibutuhkan untuk mengekstrak audio?", "answer": "Waktu ekstraksi tergantung pada ukuran file video. Untuk video yang tipikal, diperlukan sekitar 30 detik untuk mengekstrak audio."}, "supportedFormats": {"question": "<PERSON><PERSON><PERSON><PERSON> saya mengekstrak audio dari format video apa pun?", "answer": "<PERSON>, <PERSON><PERSON> dapat mengekstrak audio dari video dalam format MP4, WebM, OGG, dan MOV. Namun, format audio keluaran akan berupa MP3."}, "dataSecurity": {"question": "Apakah data saya aman?", "answer": "<PERSON>, data Anda aman. Konversi dilakukan sepenuhnya di browser <PERSON><PERSON>, dan tidak ada file yang diunggah ke server kami."}, "useCases": {"question": "Mengapa saya ingin mengekstrak audio dari video?", "answer": "Ada banyak alasan untuk mengekstrak audio dari video: membuat podcast dari konten video, menyimpan musik dari video musik, mengekstrak efek suara untuk pengeditan, mengurangi ukuran file ketika hanya audio yang dibutuhkan, atau mengonversi kuliah video menjadi audio untuk didengarkan saat bepergian."}}}