{"title": "Извлекатель аудио из видео", "subtitle": "Извлеките аудио из ваших видео и сохраните в формате MP3.", "loading": "Загрузка...", "features": {"secure": {"title": "100% Безопасно", "description": "Конвертация в браузере - файлы никогда не покидают ваше устройство"}, "fast": {"title": "Быстро и бесплатно", "description": "Быстро, конфиденциально и совершенно бесплатно в использовании"}, "noRegistration": {"title": "Без регистрации", "description": "Регистрация или загрузка на серверы не требуется"}}, "buttons": {"selectFile": "Выберите видеофайл", "extract": "Извлечь аудио", "extracting": "Извлечение...", "download": "Скачать MP3"}, "progress": {"extracting": "Извлечение:", "completed": "Извлечение завершено за {time} секунд"}, "error": {"invalidFormat": "Пожалуйста, выберите действительный видеофайл (MP4, WebM, OGG или MOV)", "unsupportedFormat": "Неподдерживаемый видеоформат", "extractionFailed": "Не удалось извлечь аудио. Пожалуйста, попробуйте снова."}, "meta": {"title": "Бесплатный веб-браузерный инструмент для извлечения аудио из видео | UniScribe Tools", "description": "Извлекайте аудио из видеофайлов безопасно в вашем браузере. Загрузка файлов не требуется - вся обработка происходит локально на вашем устройстве. Конвертируйте видео в формат MP3 с полной конфиденциальностью. Быстро и бесплатно.", "ogImageAlt": "UniScribe Извлекатель видео в аудио"}, "faq": {"extractionTime": {"question": "Сколько времени занимает извлечение аудио?", "answer": "Время извлечения зависит от размера видеофайла. Для типичного видео извлечение аудио занимает около 30 секунд."}, "supportedFormats": {"question": "Могу ли я извлечь аудио из любого видеоформата?", "answer": "Да, вы можете извлекать аудио из видео в форматах MP4, WebM, OGG и MOV. Однако формат выходного аудио будет MP3."}, "dataSecurity": {"question": "Мои данные в безопасности?", "answer": "Да, ваши данные в безопасности. Конвертация выполняется полностью в вашем браузере, и никакие файлы не загружаются на наши серверы."}, "useCases": {"question": "Зачем мне извлекать аудио из видео?", "answer": "Существует множество причин для извлечения аудио из видео: создание подкастов из видеоконтента, сохранение музыки из музыкальных видеороликов, извлечение звуковых эффектов для редактирования, уменьшение размера файла, когда требуется только аудио, или конвертация видеолекций в аудио для прослушивания в пути."}}}